# Provably Model Context Protocol (PMCP)

A secure middleware that acts as a trusted gateway for AI agents, enabling verifiable identity, persistent logging, and secure interactions in agent-driven marketplaces.

## 🎯 Mission

Build the **Provably Model Context Protocol (PMCP)** to power secure AI agent interactions. Our first deployment will be to create a simulated marketplace where agents negotiate and execute trades with full auditability and verifiable execution.

## 🏗️ Architecture

The PMCP system consists of multiple interconnected services:

- **Action Server** - Primary MCP gateway handling agent requests and tool execution
- **Prover Server** - Generates cryptographic proofs for action verification (planned)
- **Database Layer** - PostgreSQL for persistent logging and state management
- **Agent Commerce Kit (ACK)** - Framework for agent identity and commerce capabilities

## 🛠️ Tech Stack

- **Python 3.13+** - Core runtime
- **FastMCP 2.0** - MCP server framework
- **SQLModel** - Database ORM combining SQLAlchemy and Pydantic
- **PostgreSQL** - Primary database for persistent storage
- **AsyncPG** - Async PostgreSQL driver
- **Loguru** - Structured logging
- **UV** - Dependency management
- **Agent Commerce Kit (ACK)** - Agent identity and commerce protocols

## 🚀 Quick Start

### Prerequisites

- Python 3.13+
- UV package manager
- PostgreSQL

### Installation

1. Clone the repository:
```bash
git clone https://github.com/ProvablyAI/pmcp.git
cd pmcp
```

2. Install dependencies using UV:
```bash
uv sync
```

3. Set up environment configuration:
```bash
# Copy and configure environment files
cp .env.example .env
# Edit .env with your specific settings
```

### Running the Action Server

The Action Server is the primary entry point for AI agents to interact with the PMCP system.

1. **Development Mode:**
```bash
# Set environment
export APP_ENV=local

# Run the action server
uv run python -m action_server
```

2. **Production Mode:**
```bash
# Set environment
export APP_ENV=prod

# Run
uv run python -m action_server
```

The server will start on port 8000 by default. You can verify it's running by accessing:
- Health check: `http://localhost:8000/health`
- MCP endpoint: `http://localhost:8000/mcp`

## 📝 Development Guidelines

### Commit Standards

This project follows [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/) specification for all commit messages. This enables automated versioning and changelog generation.

**Commit Message Format:**
```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**Common Types:**
- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation only changes
- `style`: Changes that do not affect the meaning of the code
- `refactor`: A code change that neither fixes a bug nor adds a feature
- `perf`: A code change that improves performance
- `test`: Adding missing tests or correcting existing tests
- `chore`: Changes to the build process or auxiliary tools

**Examples:**
```bash
feat(action-server): add request validation middleware
fix(database): resolve connection pool timeout issue
docs: update API documentation for new endpoints
chore(deps): update fastmcp to version 2.12.3
```

### Code Formatting

Before each commit, ensure your code is properly formatted by running:

```bash
# Sort imports
uv run ruff check --select I --fix

# Format code
uv run ruff format
```

### Automated Releases

This project uses automated semantic versioning and releases:
- Commits following conventional format trigger automatic version bumps
- Releases are automatically created on the main branch
- Changelog is automatically generated from commit messages

#### Required GitHub Secrets

For the automated release workflow to function properly, configure these secrets in your GitHub repository settings:

- `SENTRY_AUTH_TOKEN` - Sentry authentication token for release tracking
- `SENTRY_ORG` - Your Sentry organization slug
- `SENTRY_PROJECT` - Your Sentry project slug

The `GITHUB_TOKEN` is automatically provided by GitHub Actions.


## 🔧 Configuration

The system uses environment-based configuration with the following hierarchy:

1. `config/.env.default` - Base defaults
2. `config/.env.{APP_ENV}` - Environment-specific settings
3. `.env` - Local overrides and secrets

For key configuration variables see [`.env.example`](.env.example).

## 🗄️ Database Management

The project uses PostgreSQL with Alembic for database migrations. The migration system supports both development and production environments.

### Quick Database Setup

```bash
# Set up database for development
uv run python scripts/db.py setup -e local --create-db

# Create a new migration
uv run python scripts/db.py migrate "Add new feature"

# Apply migrations
uv run python scripts/db.py upgrade

# Check migration status
uv run python scripts/db.py status
```

### Migration Commands

```bash
# Create migration with auto-generation
uv run python database/migrations.py create -m "Migration description"

# Apply all pending migrations
uv run python database/migrations.py upgrade

# Check current migration status
uv run python database/migrations.py status

# View migration history
uv run python database/migrations.py history
```

For detailed database documentation, see [`database/README.md`](database/README.md).
