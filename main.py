import asyncio

from fastmcp import Context, FastMCP
from fastmcp.tools import Tool

mcp = FastMCP(
    name="ExternalMCPServer",
    version="1.0.0",
    auth=None,
    lifespan=None,
    on_duplicate_tools="error",
    on_duplicate_resources="warn",
    on_duplicate_prompts="replace",
    include_fastmcp_meta=True,
)


async def send_hello(name: str, ctx: Context) -> str:
    result = await ctx.elicit(message="Are you sure?", response_type=None)
    if result == "decline":
        return "Declined"
    elif result.action == "accept":
        return f"Hello {name}!"
    return "Cancelled"


send_hello_tool = Tool.from_function(
    fn=send_hello,
    name="send_hello",
    description="Send a hello message",
    enabled=True,
)

mcp.add_tool(send_hello_tool)


async def main():
    await mcp.run_async(
        transport="http", port=8001, show_banner=False, log_level="info"
    )


if __name__ == "__main__":
    asyncio.run(main())
