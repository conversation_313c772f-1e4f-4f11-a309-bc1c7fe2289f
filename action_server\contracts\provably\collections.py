from enum import Enum
from uuid import UUID

from pydantic import BaseModel, Field


class SortOrder(str, Enum):
    ASCENDING = "asc"
    DESCENDING = "desc"


class PublicityStatus(str, Enum):
    PUBLIC = "public"
    PRIVATE = "private"
    LISTED = "listed"
    DRAFT = "draft"


class CollectionSortBy(str, Enum):
    Alphabetical = "alphabetical"
    QUERY_COUNT = "query_count"
    CREDITS_EARNED = "credits_earned"


# Request Models for Query Parameters
class CollectionQueryParams(BaseModel):
    collection_ids: str | None = None
    column_ids: str | None = None
    database_ids: str | None = None
    middleware_ids: str | None = None
    schema_ids: str | None = None
    table_ids: str | None = None
    collection_column_ids: str | None = None
    page: int | None = Field(None, ge=0)
    page_size: int | None = Field(None, ge=0)
    sort_order: SortOrder | None = None
    sort_by: CollectionSortBy | None = None
    query: str | None = Field(
        None, description="Search query for collection name or description"
    )
    is_valid: bool | None = None
    publicity_status: PublicityStatus | None = None


class CollectionResponse(BaseModel):
    id: UUID
    image: str | None = None
    name: str
    description: str | None = None
    query_count: int
    column_count: int
    row_count: int | None = None
    is_valid: bool
    publicity_status: PublicityStatus
    is_creator: bool
    is_creator_admin: bool
    can_query: bool


class EnabledColumn(BaseModel):
    id: UUID


class CreateCollectionRequest(BaseModel):
    name: str
    description: str | None = None
    middleware_id: UUID
    database_id: UUID
    schema_id: UUID
    table_id: UUID
    enabled_columns: list[EnabledColumn]
    is_descriptions_generated: bool
