import os
import sys
from logging.config import fileConfig

from sqlalchemy import URL, engine_from_config, pool

from alembic import context

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import project configuration and models
from action_server.core.config import Config
from database.base import db_metadata

# Import all models to ensure they're registered with SQLModel


# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Set the SQLAlchemy URL from our project configuration
db_url = URL.create(
    drivername="postgresql+psycopg2",
    username=Config.DB_USER,
    password=Config.DB_PASSWORD.get_secret_value(),
    host=Config.DB_HOST,
    port=Config.DB_PORT,
    database=Config.DB_NAME,
)
config.set_main_option("sqlalchemy.url", db_url.render_as_string(hide_password=False))

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = db_metadata


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """

    try:
        connectable = engine_from_config(
            config.get_section(config.config_ini_section, {}),
            prefix="sqlalchemy.",
            poolclass=pool.NullPool,
        )
        with connectable.connect() as connection:
            context.configure(connection=connection, target_metadata=target_metadata)
            with context.begin_transaction():
                context.run_migrations()

    except Exception as e:
        print(f"Error connecting to database: {e}")
        print("Falling back to offline mode...")
        run_migrations_offline()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
