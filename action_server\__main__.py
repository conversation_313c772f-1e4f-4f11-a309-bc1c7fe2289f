import asyncio

from fastmcp import FastMCP
from loguru import logger
from starlette.requests import Request
from starlette.responses import JSONResponse

from action_server.core.config import Config
from action_server.core.logging import setup_logging
from action_server.middlewares import ErrorHandlingMiddleware, RequestLoggingMiddleware
from action_server.middlewares.api_key_middleware import APIKeyMiddleware
from action_server.tools import greet, provably

instructions = (
    "This is the Provably Model Context Protocol (PMCP) Action Server. "
    "It acts as a trusted gateway for AI agents to interact with external systems. "
    "It handles agent identity, logs all actions for auditability, "
    "and ensures verifiable execution. Agents can use its tools for querying resources, "
    "executing trades, and managing their wallets. "
    "All actions performed through this server are persistently logged and "
    "provable by the PMCP Prover Server."
)

action_mcp = FastMCP(
    name="ActionServer",
    version="1.0.0",
    auth=None,
    lifespan=None,
    instructions=instructions,
    on_duplicate_tools="error",
    on_duplicate_resources="warn",
    on_duplicate_prompts="replace",
    include_fastmcp_meta=True,
)


@action_mcp.custom_route("/health", methods=["GET"])
async def health_check(request: Request) -> JSONResponse:
    return JSONResponse(
        {
            "status": "healthy",
            "service": "pmcp-action-mcp-server",
            "version": Config.VERSION,
        }
    )


action_mcp.add_tool(greet.send_hello_tool)

# Add Provably Collection tools

# Organizations
action_mcp.add_tool(provably.organizations.list_organizations_tool)
action_mcp.add_tool(provably.organizations.add_organization_tool)
action_mcp.add_tool(provably.organizations.get_organization_tool)
action_mcp.add_tool(provably.organizations.delete_organization_tool)
action_mcp.add_tool(provably.organizations.update_organization_tool)
action_mcp.add_tool(provably.organizations.leave_organization_tool)
action_mcp.add_tool(provably.organizations.get_users_in_organization_tool)
action_mcp.add_tool(provably.organizations.update_organization_user_tool)
action_mcp.add_tool(provably.organizations.bulk_invite_users_to_organization_tool)
action_mcp.add_tool(provably.organizations.bulk_delete_users_from_organization_tool)

# Collections
action_mcp.add_tool(provably.collections.list_collections_tool)
action_mcp.add_tool(provably.collections.create_collection_tool)

# User
action_mcp.add_tool(provably.user.get_current_user_tool)
action_mcp.add_tool(provably.user.update_current_user_tool)
action_mcp.add_tool(provably.user.delete_current_user_tool)
action_mcp.add_tool(provably.user.intiate_delete_current_user_tool)
action_mcp.add_tool(provably.user.search_user_tool)
action_mcp.add_tool(provably.user.get_user_api_key_tool)

# Middlewares
action_mcp.add_middleware(ErrorHandlingMiddleware())
action_mcp.add_middleware(RequestLoggingMiddleware())
action_mcp.add_middleware(APIKeyMiddleware())


async def main():
    # --- On Startup ---
    setup_logging()
    logger.info("Starting PMCP Action Server")

    # _ = await init_db()

    logger.success(
        f"{Config.PROJECT_NAME} v{Config.VERSION} started successfully in {Config.ENVIRONMENT} mode."
    )

    await action_mcp.run_async(
        transport="http",
        port=Config.MCP_PORT,
        path="/pmcp",
        show_banner=False,
        log_level="critical",
    )

    # --- On Shutdown ---
    logger.info("Application is shutting down...")


if __name__ == "__main__":
    asyncio.run(main())
