#!/usr/bin/env python3
"""
Database management shortcuts for PMCP.

This script provides convenient shortcuts for common database operations.
"""

import subprocess
import sys
from pathlib import Path

import click

PROJECT_ROOT = Path(__file__).parent.parent


def run_command(cmd: list[str], description: str):
    """Run a command and handle errors."""
    click.echo(f"🔄 {description}...")

    try:
        result = subprocess.run(cmd, cwd=PROJECT_ROOT, capture_output=True, text=True)

        if result.returncode == 0:
            click.echo(f"✅ {description} completed")
            if result.stdout.strip():
                click.echo(result.stdout)
        else:
            click.echo(f"❌ {description} failed:")
            click.echo(result.stderr)
            sys.exit(1)

    except Exception as e:
        click.echo(f"❌ Error during {description}: {e}")
        sys.exit(1)


@click.group()
def cli():
    """Database shortcuts for PMCP development."""
    pass


@cli.command()
@click.argument("message")
def migrate(message):
    """Create a new migration with auto-generation."""
    run_command(
        ["uv", "run", "python", "database/migrations.py", "create", "-m", message],
        f"Creating migration: {message}",
    )


@cli.command()
def upgrade():
    """Apply all pending migrations."""
    run_command(
        ["uv", "run", "python", "database/migrations.py", "upgrade"],
        "Applying migrations",
    )


@cli.command()
@click.argument("revision")
def downgrade(revision):
    """Downgrade to a specific revision."""
    run_command(
        ["uv", "run", "python", "database/migrations.py", "downgrade", "-r", revision],
        f"Downgrading to revision {revision}",
    )


@cli.command()
def status():
    """Show migration status."""
    run_command(
        ["uv", "run", "python", "database/migrations.py", "status"],
        "Checking migration status",
    )


@cli.command()
def history():
    """Show migration history."""
    run_command(
        ["uv", "run", "python", "database/migrations.py", "history"],
        "Showing migration history",
    )


@cli.command()
def setup():
    """Set up database for development."""
    run_command(
        ["uv", "run", "python", "scripts/setup_database.py", "setup"],
        "Setting up database",
    )


@cli.command()
def test():
    """Test database and migrations."""
    run_command(
        ["uv", "run", "python", "scripts/test_migrations.py"],
        "Testing database and migrations",
    )


@cli.command()
def reset():
    """Reset database (DANGEROUS)."""
    if click.confirm("This will delete all data. Continue?"):
        run_command(
            ["uv", "run", "python", "scripts/setup_database.py", "reset"],
            "Resetting database",
        )


@cli.command()
def shell():
    """Open database shell (psql)."""
    # This would need database connection details
    click.echo("Database shell not implemented yet")
    click.echo("Use: psql -h localhost -U username -d database_name")


if __name__ == "__main__":
    cli()
