import asyncio
from typing import cast

from fastmcp import Client
from fastmcp.client.client import CallToolResult

client = Client("http://127.0.0.1:8000/pmcp")


async def list_tools():
    async with client:
        tools = await client.list_tools()
        for tool in tools:
            print(f"Tool: {tool.name}")
            print(f"Description: {tool.description}")
            if tool.inputSchema:
                print(f"Parameters: {tool.inputSchema}")
            # Access tags and other metadata
            if hasattr(tool, "meta") and tool.meta:
                fastmcp_meta = tool.meta.get("_fastmcp", {})
                print(f"Tags: {fastmcp_meta.get('tags', [])}")


async def send_hello(name: str):
    async with client:
        result = await client.call_tool("send_hello", {"name": name})
        print(result)


async def list_organizations():
    async with client:
        result = await client.call_tool("provably_list_organizations")
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def list_collections():
    async with client:
        result = await client.call_tool(
            "provably_list_collections",
            arguments={
                "organization_id": "989d8ac0-ded4-4e1e-9351-0fa0c27ad17d",
                "params": {"page_size": 5},
            },
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def get_current_user():
    async with client:
        result = await client.call_tool("provably_get_current_user")
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def search_user():
    async with client:
        result = await client.call_tool(
            "provably_search_user", arguments={"query_params": {"query": "demo"}}
        )
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def get_user_api_key():
    async with client:
        result = await client.call_tool("provably_get_user_api_key")
        result = cast(CallToolResult, result)
        print(f"{type(result)=}")
        print(result.data)


async def main():
    # await list_organizations()
    await list_collections()
    # await get_current_user()
    # await search_user()
    # await get_user_api_key()
    # await send_hello("John")


async def test_list_collections():
    """Test listing collections through PMCP Action Server."""

    # Prepare request (API key will come from headers via middleware)
    # query_params=CollectionQueryParams(
    #     page=0,
    #     page_size=5,
    #     query="basketball"
    # )

    try:
        async with client:
            # Call the provably_list_collections tool
            # Note: API key should be provided via headers when middleware is implemented
            result = await client.call_tool(
                "provably_list_collections",
                arguments={
                    "query_params": {
                        "query": "basketball",
                        "page": 0,
                        "page_size": 5,
                        "sort_by": "alphabetical",
                        "sort_order": "desc",
                    }
                },
            )

            result = cast(CallToolResult, result)

            print("Collections retrieved successfully:")
            print(f"{type(result)=}")
            print(result.data)

    except Exception as e:
        print(f"Error calling Provably API: {e}")


asyncio.run(main())
