from fastmcp.server.middleware import <PERSON>Next, Middleware, MiddlewareContext

from action_server.core.config import Config
from shared.exceptions import PMCPPermissionError


class APIKeyMiddleware(Middleware):
    """Temporary middleware to extract and validate API keys from request headers."""

    async def on_call_tool(self, context: MiddlewareContext, call_next: CallNext):
        """Extract API key and store in context."""

        api_key = Config.PROVABLY_API_KEY.get_secret_value()

        context.fastmcp_context.set_state("api_key", api_key)

        if not api_key:
            raise PMCPPermissionError("API key required")

        return await call_next(context)
