from datetime import datetime
from enum import Enum
from uuid import UUID

from pydantic import BaseModel, Field


class TypeOfOrganization(str, Enum):
    DEMOGRAPHICS = "demographics"
    E_COMMERCE = "e-commerce"
    SOCIAL_MEDIA = "social-media"
    HEALTH_AND_FITNESS = "health-and-fitness"
    CLIMATE_AND_WEATHER = "climate-and-weather"
    EDUCATION = "education"
    FINANCIAL = "financial"
    REAL_ESTATE = "real-estate"
    ENERGY_CONSUMPTION = "energy-consumption"
    SPORTS = "sports"
    RETAIL = "retail"
    HEALTHCARE = "healthcare"
    CRYPTOCURRENCY = "cryptocurrency"
    GOVERNMENT = "government"
    ENTERTAINMENT = "entertainment"


class OrganizationResponse(BaseModel):
    id: UUID
    name: str
    description: str | None
    type: TypeOfOrganization
    collection_count: int


class OrganizationParams(BaseModel):
    name: str
    description: str | None = None
    type: TypeOfOrganization


class UsersSortOrder(str, Enum):
    ASCENDING = "asc"
    DESCENDING = "desc"


class UsersSortBy(str, Enum):
    USER = "user"
    ROLE = "role"
    JOINED_AT = "joined_at"


class GetUsersParams(BaseModel):
    page: int | None = Field(None, ge=0)
    page_size: int | None = Field(None, ge=0)
    sort_order: UsersSortOrder | None = None
    sort_by: UsersSortBy | None = None
    query: str | None = Field(None, description="Search query for user name or email")


class UserRole(str, Enum):
    ADMIN = "admin"
    OWNER = "owner"
    DEVELOPER = "developer"


class UserRoleForUpdate(str, Enum):
    ADMIN = "admin"
    DEVELOPER = "developer"


class UserInvitationStatus(str, Enum):
    JOINED = "joined"
    PENDING = "pending"
    EXPIRED = "expired"
    REJECTED = "rejected"


class OrganizationUser(BaseModel):
    id: UUID
    username: str
    email: str
    avatar: str | None = None
    role: UserRole
    invitation_status: UserInvitationStatus
    joined_at: datetime | None = None


class ChangeRoleParams(BaseModel):
    role: UserRoleForUpdate


class InviteUserParams(BaseModel):
    email: str | None = None
    username: str | None = None
    role: UserRoleForUpdate


class InviteUserListParams(BaseModel):
    users: list[InviteUserParams]
