from uuid import UUID

from fastmcp import Context
from fastmcp.tools import Tool

from action_server.contracts.provably.collections import (
    CollectionQueryParams,
    CollectionResponse,
    CreateCollectionRequest,
)
from action_server.services.provably_client import ProvablyHTTPClientV1

# Initialize HTTP client
client = ProvablyHTTPClientV1()


async def list_collections(
    organization_id: UUID, params: CollectionQueryParams, ctx: Context
) -> list[CollectionResponse]:
    """List collections with optional filtering and pagination."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint=f"/organizations/{organization_id}/collections_frontend",
        api_key=api_key,
        params=params.model_dump(mode="json", exclude_none=True),
    )
    return [CollectionResponse(**item) for item in response]


list_collections_tool = Tool.from_function(
    fn=list_collections,
    name="provably_list_collections",
    description="List Provably collections with optional filtering and pagination",
    enabled=True,
)


async def create_collection(
    organization_id: UUID, body: CreateCollectionRequest, ctx: Context
) -> dict:
    """Create a new collection."""
    api_key = ctx.get_state("api_key")
    response = await client.post(
        endpoint=f"/organizations/{organization_id}/collections",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return response


create_collection_tool = Tool.from_function(
    fn=create_collection,
    name="provably_create_collection",
    description="Create a new collection",
    enabled=True,
)
