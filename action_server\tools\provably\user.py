from fastmcp import Context
from fastmcp.tools import Tool

from action_server.contracts.provably.user import (
    ApiKeyResponse,
    CurrentResponse,
    DeleteUserParams,
    SearchRequest,
    SearchResponse,
    UpdateUserParams,
)
from action_server.services.provably_client import ProvablyHTTPClientV1

client = ProvablyHTTPClientV1()


async def get_current_user(ctx: Context) -> CurrentResponse:
    """Get current user information."""
    api_key = ctx.get_state("api_key")
    response = await client.get(endpoint="/user/current", api_key=api_key)
    return CurrentResponse(**response)


get_current_user_tool = Tool.from_function(
    fn=get_current_user,
    name="provably_get_current_user",
    description="Get current user information",
    enabled=True,
)


async def update_current_user(body: UpdateUserParams, ctx: Context) -> CurrentResponse:
    """Update current user information."""
    api_key = ctx.get_state("api_key")
    response = await client.patch(
        endpoint="/user/current",
        api_key=api_key,
        data=body.model_dump(mode="json", exclude_none=True),
    )
    return CurrentResponse(**response)


update_current_user_tool = Tool.from_function(
    fn=update_current_user,
    name="provably_update_current_user",
    description="Update current user information",
    enabled=True,
)


async def delete_current_user(params: DeleteUserParams, ctx: Context) -> dict:
    """Delete current user."""
    api_key = ctx.get_state("api_key")
    response = await client.delete(
        endpoint="/user/current",
        api_key=api_key,
        params=params.model_dump(mode="json", exclude_none=True),
    )
    return response


delete_current_user_tool = Tool.from_function(
    fn=delete_current_user,
    name="provably_delete_current_user",
    description="Delete current user",
    enabled=True,
)


async def intiate_delete_current_user(ctx: Context) -> dict:
    """Initiate deletion of current user."""
    api_key = ctx.get_state("api_key")
    response = await client.post(
        endpoint="/user/current/initiate_delete", api_key=api_key
    )
    return response


intiate_delete_current_user_tool = Tool.from_function(
    fn=intiate_delete_current_user,
    name="provably_intiate_delete_current_user",
    description="Initiate deletion of current user",
    enabled=True,
)


async def search_user(params: SearchRequest, ctx: Context) -> SearchResponse:
    """Search user."""
    api_key = ctx.get_state("api_key")
    response = await client.get(
        endpoint="/user/search",
        api_key=api_key,
        params=params.model_dump(mode="json", exclude_none=True),
    )
    return SearchResponse(**response)


search_user_tool = Tool.from_function(
    fn=search_user,
    name="provably_search_user",
    description="Search user",
    enabled=True,
)


async def get_user_api_key(ctx: Context) -> ApiKeyResponse:
    """Get user API key."""
    api_key = ctx.get_state("api_key")
    response = await client.get(endpoint="/user/key", api_key=api_key)
    return ApiKeyResponse(**response)


get_user_api_key_tool = Tool.from_function(
    fn=get_user_api_key,
    name="provably_get_user_api_key",
    description="Get user API key",
    enabled=True,
)
