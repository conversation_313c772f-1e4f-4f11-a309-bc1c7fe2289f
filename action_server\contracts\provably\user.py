from pydantic import BaseModel


class CurrentResponse(BaseModel):
    pid: str
    name: str
    email: str
    is_admin: bool
    username: str
    first_name: str | None = None
    last_name: str | None = None
    avatar: str | None = None
    has_generated_api_key: bool


class UpdateUserParams(BaseModel):
    username: str | None = None
    first_name: str | None = None
    last_name: str | None = None
    avatar: str | bytes | None = None


class DeleteUserParams(BaseModel):
    code: str | None = None


class SearchRequest(BaseModel):
    query: str


class SearchResponse(BaseModel):
    email: str | None = None
    username: str | None = None
    avatar: str | None = None


class ApiKeyResponse(BaseModel):
    api_key: str | None = None
