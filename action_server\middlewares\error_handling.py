import asyncio
import time
from typing import Any, cast

import pydantic_core
from fastmcp.exceptions import ToolError
from fastmcp.server.middleware import CallNext, Middleware, MiddlewareContext
from loguru import logger
from mcp import McpError
from mcp.types import ErrorData

from shared.exceptions import (
    ContentTooLargeError,
    InvalidParamsError,
    InvalidRequestError,
    MethodNotFoundError,
    ParseError,
    PMCPPermissionError,
    RequestCancelledError,
    ResourceUnavailableError,
)

from .request_logging import RequestContextData


def default_serializer(data: Any) -> str:
    return pydantic_core.to_json(data, fallback=str).decode()


def map_error_to_mcp_error(error: Exception) -> Exception:
    """Transform non-MCP errors to proper MCP errors with security considerations."""
    if isinstance(error, McpError):
        return error

    error_type = type(error)
    error_message = str(error)

    # Map to appropriate MCP error codes
    if error_type in (
        ResourceUnavailableError,
        ConnectionError,
        TimeoutError,
        asyncio.TimeoutError,
    ):
        return McpError(ErrorData(code=-32000, message="Resource unavailable"))
    elif error_type is PMCPPermissionError:
        return McpError(
            ErrorData(code=-32002, message=f"Permission denied: {error_message}")
        )
    elif error_type in (InvalidRequestError, ToolError):
        return McpError(
            ErrorData(code=-32600, message=f"Invalid request: {error_message}")
        )
    elif error_type is MethodNotFoundError:
        return McpError(
            ErrorData(code=-32601, message=f"Method not found: {error_message}")
        )
    elif error_type is InvalidParamsError:
        return McpError(
            ErrorData(code=-32602, message=f"Invalid params: {error_message}")
        )
    elif error_type is ParseError:
        return McpError(ErrorData(code=-32700, message=f"Parse error: {error_message}"))
    elif error_type is RequestCancelledError:
        return McpError(
            ErrorData(code=-32800, message=f"Request cancelled: {error_message}")
        )
    elif error_type is ContentTooLargeError:
        return McpError(
            ErrorData(code=-32801, message=f"Content too large: {error_message}")
        )
    elif error_type is ValueError and "Invalid JSON" in error_message:
        return McpError(
            ErrorData(code=-32602, message=f"Invalid JSON parameter: {error_message}")
        )
    elif error_type is ValueError and "Invalid data for parameter" in error_message:
        return McpError(
            ErrorData(
                code=-32602, message=f"Parameter validation failed: {error_message}"
            )
        )
    elif error_type is ValueError and "Cannot convert parameter" in error_message:
        return McpError(
            ErrorData(
                code=-32602, message=f"Parameter conversion failed: {error_message}"
            )
        )
    else:
        # All other errors are treated as internal errors
        return McpError(
            ErrorData(
                code=-32603,
                message="Internal error occurred while processing your request",
            )
        )


class ErrorHandlingMiddleware(Middleware):
    async def on_message(self, context: MiddlewareContext, call_next: CallNext):
        try:
            return await call_next(context)
        except Exception as e:
            # Safely get context data, handle case where it might not exist
            context_data = None
            if context.fastmcp_context:
                context_data = cast(
                    RequestContextData,
                    context.fastmcp_context.get_state("pmcp_context_data"),
                )

            # Prepare error logging details
            error_type = type(e).__name__
            error_log_details: dict[str, Any] = {
                "error_type": error_type,
                "error_message": str(e),
            }

            if context_data:
                start_time = context_data.start_time
                request_details = context_data.request_details
                process_time_ms = round((time.perf_counter() - start_time) * 1000, 3)

                # Merge request details and additional info
                request_dict = request_details.model_dump()
                error_log_details.update(request_dict)
                error_log_details.update(
                    {
                        "duration_ms": process_time_ms,
                        "session_id": str(context_data.session_id),
                        "request_id": str(context_data.request_id),
                    }
                )

                if context_data.action_record_id:
                    error_log_details["action_id"] = str(context_data.action_record_id)

            # Include additional context if available
            agent_identity = getattr(context, "pmcp_agent_identity", None)
            if agent_identity:
                error_log_details["agent_identity"] = agent_identity

            logger.error(
                f"Error while processing a '{getattr(context, 'method', 'unknown')}' request: {error_type}: {str(e)}",
                **error_log_details,
            )

            # Transform and re-raise
            transformed_error = map_error_to_mcp_error(e)
            raise transformed_error
